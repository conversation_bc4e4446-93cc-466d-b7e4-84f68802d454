// File: Program.cs

using System.Net;
using System.Net.Sockets;
using System.Text;

// Check if we should run tests
if (args.Length > 0 && args[0] == "test")
{
    Stage6Test.RunTest();
    return;
}

// Main program entry point and server logic
await StartServer();

async Task StartServer()
{
    using var udpClient = new UdpClient(new IPEndPoint(IPAddress.Any, 2053));
    Console.WriteLine("DNS server listening on port 2053...");

    try
    {
        while (true)
        {
            UdpReceiveResult receiveResult = await udpClient.ReceiveAsync();
            Console.WriteLine($"Received packet from {receiveResult.RemoteEndPoint}");

            // 1. Parse the incoming request to get the question.
            DnsMessage requestMessage = DnsPacketSerializer.FromByteArray(receiveResult.Buffer);
            var question = requestMessage.Questions.FirstOrDefault();
            if (question == null) continue; // Ignore packets with no questions

            Console.WriteLine($"Received query for: {question.Name}");

            // 2. Create the response message.
            var responseMessage = new DnsMessage();

            // 3. Build the response header.
            responseMessage.Header.PacketIdentifier = requestMessage.Header.PacketIdentifier;
            responseMessage.Header.RecursionDesired = requestMessage.Header.RecursionDesired;
            responseMessage.Header.OpCode = requestMessage.Header.OpCode;
            responseMessage.Header.QueryResponse = true;
            responseMessage.Header.ResponseCode = requestMessage.Header.OpCode == 0 ? (byte)0 : (byte)4;

            // 4. Copy the question from the request to the response.
            responseMessage.Questions.Add(question);
            responseMessage.Header.QuestionCount = 1;

            // 5. Create a corresponding answer record.
            responseMessage.Answers.Add(new DnsResourceRecord
            {
                Name = question.Name, // The name is now dynamic from the question
                Type = 1, // A Record
                Class = 1, // IN (Internet)
                Ttl = 60,
                RdLength = 4,
                Rdata = new byte[] { 8, 8, 8, 8 } // Still a hardcoded IP for now
            });
            responseMessage.Header.AnswerRecordCount = 1;

            // 6. Serialize and send the response.
            byte[] responseBytes = DnsPacketSerializer.ToByteArray(responseMessage);
            await udpClient.SendAsync(responseBytes, responseBytes.Length, receiveResult.RemoteEndPoint);
            Console.WriteLine($"Sent response for {question.Name} to {receiveResult.RemoteEndPoint}");
        }
    }
    catch (SocketException e)
    {
        Console.WriteLine($"SocketException: {e.Message}");
    }
}

/// <summary>
/// Represents the 12-byte header of a DNS message.
/// </summary>
public class DnsHeader
{
    public ushort PacketIdentifier { get; set; } // 16 bits
    public bool QueryResponse { get; set; }      // 1 bit (0 for query, 1 for response)
    public byte OpCode { get; set; }             // 4 bits (0 for standard query)
    public bool AuthoritativeAnswer { get; set; }// 1 bit
    public bool Truncation { get; set; }         // 1 bit
    public bool RecursionDesired { get; set; }   // 1 bit
    public bool RecursionAvailable { get; set; }// 1 bit
    public byte ResponseCode { get; set; }       // 4 bits (0 for no error)
    public ushort QuestionCount { get; set; }    // 16 bits
    public ushort AnswerRecordCount { get; set; }// 16 bits
    public ushort AuthorityRecordCount { get; set; } // 16 bits
    public ushort AdditionalRecordCount { get; set; } // 16 bits
}

/// <summary>
/// Represents a question in the DNS message's question section.
/// </summary>
public class DnsQuestion
{
    public string Name { get; set; } = string.Empty;
    public ushort Type { get; set; }
    public ushort Class { get; set; }
}

/// <summary>
/// Represents a Resource Record (RR) in the answer, authority, or additional sections.
/// </summary>
public class DnsResourceRecord
{
    public string Name { get; set; } = string.Empty;
    public ushort Type { get; set; }
    public ushort Class { get; set; }
    public uint Ttl { get; set; }
    public ushort RdLength { get; set; }
    public byte[] Rdata { get; set; } = Array.Empty<byte>();
}

/// <summary>
/// Represents a full DNS message.
/// </summary>
public class DnsMessage
{
    public DnsHeader Header { get; set; } = new();
    public List<DnsQuestion> Questions { get; set; } = new();
    public List<DnsResourceRecord> Answers { get; set; } = new();
}

/// <summary>
/// Handles serialization and deserialization of DNS message objects.
/// </summary>
public static class DnsPacketSerializer
{
    public static byte[] ToByteArray(DnsMessage message)
    {
        var stream = new MemoryStream();
        // Using a BinaryWriter helps, but we must manage endianness manually for network order.
        using (var writer = new BinaryWriter(stream))
        {
            // --- Header Section (12 bytes) ---

            // Packet Identifier (ID) - 16 bits
            writer.Write(IPAddress.HostToNetworkOrder((short)message.Header.PacketIdentifier));

            // Flags - 16 bits total, split into two bytes
            // Format: QR(1) OPCODE(4) AA(1) TC(1) RD(1) RA(1) Z(3) RCODE(4)
            ushort flags = 0;
            if (message.Header.QueryResponse) flags |= (1 << 15);        // QR bit 15
            flags |= (ushort)(message.Header.OpCode << 11);              // OPCODE bits 14-11
            if (message.Header.AuthoritativeAnswer) flags |= (1 << 10);  // AA bit 10
            if (message.Header.Truncation) flags |= (1 << 9);            // TC bit 9
            if (message.Header.RecursionDesired) flags |= (1 << 8);      // RD bit 8
            if (message.Header.RecursionAvailable) flags |= (1 << 7);    // RA bit 7
            // Z bits 6-4 are reserved and should be 0
            flags |= (ushort)(message.Header.ResponseCode & 0x0F);       // RCODE bits 3-0
            writer.Write(IPAddress.HostToNetworkOrder((short)flags));

            // Question Count (QDCOUNT) - 16 bits
            writer.Write(IPAddress.HostToNetworkOrder((short)message.Header.QuestionCount));

            // Answer Record Count (ANCOUNT) - 16 bits
            writer.Write(IPAddress.HostToNetworkOrder((short)message.Header.AnswerRecordCount));

            // Authority Record Count (NSCOUNT) - 16 bits
            writer.Write(IPAddress.HostToNetworkOrder((short)message.Header.AuthorityRecordCount));

            // Additional Record Count (ARCOUNT) - 16 bits
            writer.Write(IPAddress.HostToNetworkOrder((short)message.Header.AdditionalRecordCount));

            // --- Question Section ---
            foreach (var question in message.Questions)
            {
                EncodeDomainName(writer, question.Name);
                writer.Write(IPAddress.HostToNetworkOrder((short)question.Type));
                writer.Write(IPAddress.HostToNetworkOrder((short)question.Class));
            }

            // --- Answer Section ---
            foreach (var answer in message.Answers)
            {
                EncodeDomainName(writer, answer.Name);
                writer.Write(IPAddress.HostToNetworkOrder((short)answer.Type));
                writer.Write(IPAddress.HostToNetworkOrder((short)answer.Class));
                writer.Write(IPAddress.HostToNetworkOrder((int)answer.Ttl));
                writer.Write(IPAddress.HostToNetworkOrder((short)answer.RdLength));
                writer.Write(answer.Rdata);
            }
        }
        return stream.ToArray();
    }

    /// <summary>
    /// Encodes a domain name like "google.com" into the DNS label format:
    /// 6, 'g','o','o','g','l','e', 3, 'c','o','m', 0
    /// </summary>
    private static void EncodeDomainName(BinaryWriter writer, string domainName)
    {
        if (string.IsNullOrEmpty(domainName) || domainName == ".")
        {
            writer.Write((byte)0); // Null terminator for root domain
            return;
        }

        var labels = domainName.Split('.');
        foreach (var label in labels)
        {
            var labelBytes = Encoding.ASCII.GetBytes(label);
            writer.Write((byte)labelBytes.Length);
            writer.Write(labelBytes);
        }
        writer.Write((byte)0); // Null terminator for the entire name
    }

    /// <summary>
    /// Deserializes a byte array into a DnsMessage object.
    /// Now parses both header and question sections.
    /// </summary>
    public static DnsMessage FromByteArray(byte[] data)
    {
        var message = new DnsMessage();
        var stream = new MemoryStream(data);
        using (var reader = new BinaryReader(stream))
        {
            // --- Header Section (12 bytes) ---

            // Packet Identifier (ID) - 16 bits
            message.Header.PacketIdentifier = (ushort)IPAddress.NetworkToHostOrder(reader.ReadInt16());

            // Flags - 16 bits total
            ushort flags = (ushort)IPAddress.NetworkToHostOrder(reader.ReadInt16());

            // Extract individual flags using bitwise operations
            message.Header.QueryResponse = (flags & 0b1000_0000_0000_0000) != 0;        // QR bit 15
            message.Header.OpCode = (byte)((flags >> 11) & 0b0000_1111);               // OPCODE bits 14-11
            message.Header.AuthoritativeAnswer = (flags & 0b0000_0100_0000_0000) != 0; // AA bit 10
            message.Header.Truncation = (flags & 0b0000_0010_0000_0000) != 0;          // TC bit 9
            message.Header.RecursionDesired = (flags & 0b0000_0001_0000_0000) != 0;    // RD bit 8
            message.Header.RecursionAvailable = (flags & 0b0000_0000_1000_0000) != 0;  // RA bit 7
            // Z bits 6-4 are reserved and ignored
            message.Header.ResponseCode = (byte)(flags & 0b0000_0000_0000_1111);       // RCODE bits 3-0

            // Question Count (QDCOUNT) - 16 bits
            message.Header.QuestionCount = (ushort)IPAddress.NetworkToHostOrder(reader.ReadInt16());

            // Answer Record Count (ANCOUNT) - 16 bits
            message.Header.AnswerRecordCount = (ushort)IPAddress.NetworkToHostOrder(reader.ReadInt16());

            // Authority Record Count (NSCOUNT) - 16 bits
            message.Header.AuthorityRecordCount = (ushort)IPAddress.NetworkToHostOrder(reader.ReadInt16());

            // Additional Record Count (ARCOUNT) - 16 bits
            message.Header.AdditionalRecordCount = (ushort)IPAddress.NetworkToHostOrder(reader.ReadInt16());

            // --- Question Section ---
            for (int i = 0; i < message.Header.QuestionCount; i++)
            {
                message.Questions.Add(new DnsQuestion
                {
                    Name = DecodeDomainName(reader),
                    Type = (ushort)IPAddress.NetworkToHostOrder(reader.ReadInt16()),
                    Class = (ushort)IPAddress.NetworkToHostOrder(reader.ReadInt16())
                });
            }
        }
        return message;
    }

    /// <summary>
    /// Decodes a domain name from a byte stream.
    /// Handles the label format: <length><content><length><content>...<null>
    /// </summary>
    private static string DecodeDomainName(BinaryReader reader)
    {
        var labels = new List<string>();
        byte length;
        while ((length = reader.ReadByte()) != 0)
        {
            labels.Add(Encoding.ASCII.GetString(reader.ReadBytes(length)));
        }
        return string.Join('.', labels);
    }
}

// Stage 6 Test: Verify question section parsing
public static class Stage6Test
{
    public static void RunTest()
    {
        Console.WriteLine("=== Stage 6 Test: Parse Question Section ===\n");

        try
        {
            TestQuestionParsing();
            TestMultipleQuestions();
            TestDomainNameDecoding();
            Console.WriteLine("🎉 ALL STAGE 6 TESTS PASSED!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ TEST FAILED: {ex.Message}");
            Environment.Exit(1);
        }
    }

    static void TestQuestionParsing()
    {
        Console.WriteLine("Test 1: Basic Question Parsing");

        // Create a DNS packet with a question for "example.com"
        var packet = new List<byte>();

        // Header (12 bytes)
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)0x1234))); // ID
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)0x0100))); // Flags (RD=1)
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)1)));      // QDCOUNT=1
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)0)));      // ANCOUNT=0
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)0)));      // NSCOUNT=0
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)0)));      // ARCOUNT=0

        // Question section for "example.com"
        // Domain name: 7 "example" 3 "com" 0
        packet.Add(7);
        packet.AddRange(Encoding.ASCII.GetBytes("example"));
        packet.Add(3);
        packet.AddRange(Encoding.ASCII.GetBytes("com"));
        packet.Add(0);

        // Type: A (1) and Class: IN (1)
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)1))); // Type A
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)1))); // Class IN

        var message = DnsPacketSerializer.FromByteArray(packet.ToArray());

        Assert(message.Header.PacketIdentifier == 0x1234, "Packet ID should be 0x1234");
        Assert(message.Header.QuestionCount == 1, "Should have 1 question");
        Assert(message.Questions.Count == 1, "Questions list should have 1 item");
        Assert(message.Questions[0].Name == "example.com", "Question name should be 'example.com'");
        Assert(message.Questions[0].Type == 1, "Question type should be 1 (A)");
        Assert(message.Questions[0].Class == 1, "Question class should be 1 (IN)");

        Console.WriteLine("✓ Basic question parsing test passed\n");
    }

    static void TestMultipleQuestions()
    {
        Console.WriteLine("Test 2: Multiple Questions");

        var packet = new List<byte>();

        // Header with 2 questions
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)0x5678))); // ID
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)0x0100))); // Flags
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)2)));      // QDCOUNT=2
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)0)));      // ANCOUNT=0
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)0)));      // NSCOUNT=0
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)0)));      // ARCOUNT=0

        // First question: "test.com"
        packet.Add(4);
        packet.AddRange(Encoding.ASCII.GetBytes("test"));
        packet.Add(3);
        packet.AddRange(Encoding.ASCII.GetBytes("com"));
        packet.Add(0);
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)1))); // Type A
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)1))); // Class IN

        // Second question: "google.com"
        packet.Add(6);
        packet.AddRange(Encoding.ASCII.GetBytes("google"));
        packet.Add(3);
        packet.AddRange(Encoding.ASCII.GetBytes("com"));
        packet.Add(0);
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)1))); // Type A
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)1))); // Class IN

        var message = DnsPacketSerializer.FromByteArray(packet.ToArray());

        Assert(message.Header.QuestionCount == 2, "Should have 2 questions");
        Assert(message.Questions.Count == 2, "Questions list should have 2 items");
        Assert(message.Questions[0].Name == "test.com", "First question should be 'test.com'");
        Assert(message.Questions[1].Name == "google.com", "Second question should be 'google.com'");

        Console.WriteLine("✓ Multiple questions test passed\n");
    }

    static void TestDomainNameDecoding()
    {
        Console.WriteLine("Test 3: Domain Name Decoding Edge Cases");

        // Test single label domain
        var packet1 = CreatePacketWithDomain("localhost");
        var message1 = DnsPacketSerializer.FromByteArray(packet1);
        Assert(message1.Questions[0].Name == "localhost", "Single label domain should work");

        // Test subdomain
        var packet2 = CreatePacketWithDomain("www.example.com");
        var message2 = DnsPacketSerializer.FromByteArray(packet2);
        Assert(message2.Questions[0].Name == "www.example.com", "Subdomain should work");

        // Test long domain
        var packet3 = CreatePacketWithDomain("very.long.subdomain.example.com");
        var message3 = DnsPacketSerializer.FromByteArray(packet3);
        Assert(message3.Questions[0].Name == "very.long.subdomain.example.com", "Long domain should work");

        Console.WriteLine("✓ Domain name decoding test passed\n");
    }

    static byte[] CreatePacketWithDomain(string domain)
    {
        var packet = new List<byte>();

        // Header
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)0x1234))); // ID
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)0x0100))); // Flags
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)1)));      // QDCOUNT=1
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)0)));      // ANCOUNT=0
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)0)));      // NSCOUNT=0
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)0)));      // ARCOUNT=0

        // Encode domain name
        var labels = domain.Split('.');
        foreach (var label in labels)
        {
            packet.Add((byte)label.Length);
            packet.AddRange(Encoding.ASCII.GetBytes(label));
        }
        packet.Add(0); // Null terminator

        // Type and Class
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)1))); // Type A
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)1))); // Class IN

        return packet.ToArray();
    }

    static void Assert(bool condition, string message)
    {
        if (!condition)
        {
            throw new Exception($"ASSERTION FAILED: {message}");
        }
    }
}
