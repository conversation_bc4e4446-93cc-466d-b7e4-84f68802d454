// Test runner for Stage 6
using System.Net;
using System.Net.Sockets;
using System.Text;

public static class TestRunner
{
    public static async Task Main(string[] args)
    {
        if (args.Length > 0 && args[0] == "test")
        {
            Stage6Test.RunTest();
            return;
        }

        Console.WriteLine("Use 'dotnet run test' to run Stage 6 tests");
        Console.WriteLine("Use 'dotnet run' to start the DNS server");
    }
}
