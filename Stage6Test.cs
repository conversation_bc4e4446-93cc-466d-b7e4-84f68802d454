// Stage 6 Test: Verify question section parsing
using System.Net;
using System.Text;

public static class Stage6Test
{
    public static void RunTest()
    {
        Console.WriteLine("=== Stage 6 Test: Parse Question Section ===\n");

        try
        {
            TestQuestionParsing();
            TestMultipleQuestions();
            TestDomainNameDecoding();
            Console.WriteLine("🎉 ALL STAGE 6 TESTS PASSED!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ TEST FAILED: {ex.Message}");
            Environment.Exit(1);
        }
    }

    static void TestQuestionParsing()
    {
        Console.WriteLine("Test 1: Basic Question Parsing");

        // Create a DNS packet with a question for "example.com"
        var packet = new List<byte>();
        
        // Header (12 bytes)
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)0x1234))); // ID
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)0x0100))); // Flags (RD=1)
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)1)));      // QDCOUNT=1
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)0)));      // ANCOUNT=0
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)0)));      // NSCOUNT=0
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)0)));      // ARCOUNT=0

        // Question section for "example.com"
        // Domain name: 7 "example" 3 "com" 0
        packet.Add(7);
        packet.AddRange(Encoding.ASCII.GetBytes("example"));
        packet.Add(3);
        packet.AddRange(Encoding.ASCII.GetBytes("com"));
        packet.Add(0);
        
        // Type: A (1) and Class: IN (1)
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)1))); // Type A
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)1))); // Class IN

        var message = DnsPacketSerializer.FromByteArray(packet.ToArray());

        Assert(message.Header.PacketIdentifier == 0x1234, "Packet ID should be 0x1234");
        Assert(message.Header.QuestionCount == 1, "Should have 1 question");
        Assert(message.Questions.Count == 1, "Questions list should have 1 item");
        Assert(message.Questions[0].Name == "example.com", "Question name should be 'example.com'");
        Assert(message.Questions[0].Type == 1, "Question type should be 1 (A)");
        Assert(message.Questions[0].Class == 1, "Question class should be 1 (IN)");

        Console.WriteLine("✓ Basic question parsing test passed\n");
    }

    static void TestMultipleQuestions()
    {
        Console.WriteLine("Test 2: Multiple Questions");

        var packet = new List<byte>();
        
        // Header with 2 questions
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)0x5678))); // ID
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)0x0100))); // Flags
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)2)));      // QDCOUNT=2
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)0)));      // ANCOUNT=0
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)0)));      // NSCOUNT=0
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)0)));      // ARCOUNT=0

        // First question: "test.com"
        packet.Add(4);
        packet.AddRange(Encoding.ASCII.GetBytes("test"));
        packet.Add(3);
        packet.AddRange(Encoding.ASCII.GetBytes("com"));
        packet.Add(0);
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)1))); // Type A
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)1))); // Class IN

        // Second question: "google.com"
        packet.Add(6);
        packet.AddRange(Encoding.ASCII.GetBytes("google"));
        packet.Add(3);
        packet.AddRange(Encoding.ASCII.GetBytes("com"));
        packet.Add(0);
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)1))); // Type A
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)1))); // Class IN

        var message = DnsPacketSerializer.FromByteArray(packet.ToArray());

        Assert(message.Header.QuestionCount == 2, "Should have 2 questions");
        Assert(message.Questions.Count == 2, "Questions list should have 2 items");
        Assert(message.Questions[0].Name == "test.com", "First question should be 'test.com'");
        Assert(message.Questions[1].Name == "google.com", "Second question should be 'google.com'");

        Console.WriteLine("✓ Multiple questions test passed\n");
    }

    static void TestDomainNameDecoding()
    {
        Console.WriteLine("Test 3: Domain Name Decoding Edge Cases");

        // Test single label domain
        var packet1 = CreatePacketWithDomain("localhost");
        var message1 = DnsPacketSerializer.FromByteArray(packet1);
        Assert(message1.Questions[0].Name == "localhost", "Single label domain should work");

        // Test subdomain
        var packet2 = CreatePacketWithDomain("www.example.com");
        var message2 = DnsPacketSerializer.FromByteArray(packet2);
        Assert(message2.Questions[0].Name == "www.example.com", "Subdomain should work");

        // Test long domain
        var packet3 = CreatePacketWithDomain("very.long.subdomain.example.com");
        var message3 = DnsPacketSerializer.FromByteArray(packet3);
        Assert(message3.Questions[0].Name == "very.long.subdomain.example.com", "Long domain should work");

        Console.WriteLine("✓ Domain name decoding test passed\n");
    }

    static byte[] CreatePacketWithDomain(string domain)
    {
        var packet = new List<byte>();
        
        // Header
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)0x1234))); // ID
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)0x0100))); // Flags
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)1)));      // QDCOUNT=1
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)0)));      // ANCOUNT=0
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)0)));      // NSCOUNT=0
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)0)));      // ARCOUNT=0

        // Encode domain name
        var labels = domain.Split('.');
        foreach (var label in labels)
        {
            packet.Add((byte)label.Length);
            packet.AddRange(Encoding.ASCII.GetBytes(label));
        }
        packet.Add(0); // Null terminator
        
        // Type and Class
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)1))); // Type A
        packet.AddRange(BitConverter.GetBytes(IPAddress.HostToNetworkOrder((short)1))); // Class IN

        return packet.ToArray();
    }

    static void Assert(bool condition, string message)
    {
        if (!condition)
        {
            throw new Exception($"ASSERTION FAILED: {message}");
        }
    }
}
